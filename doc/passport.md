# Паспорт проекта

Проект «Литературный Компас» — это создание инновационной веб-платформы для каталогизации и поиска книг, основанной на глубоком семантическом анализе текста. В отличие от существующих каталогов, ограниченных поиском по автору и названию, наша система предлагает пользователям многомерный поиск по настроению, атмосфере, динамике сюжета, архетипам персонажей и другим неочевидным характеристикам. Мы не стремимся охватить ВСЕ сломанные, побитые или маленькие книги.

**Проблемы:** Читатели тратят много времени на поиск "той самой" книги, полагаясь на неточные теги и общие рекомендации. Существующие системы не способны понять запрос вроде "найди мне мрачный детектив в викторианском сеттинге с умным, но циничным главным героем".

**Решение:** Мы создаем бэкенд-систему, которая обрабатывает миллионы книг, извлекая из них суть с помощью LLM и векторных представлений (RAG). Эти обогащенные данные становятся основой для публичного веб-каталога, который выступает в роли умного советчика. **На сайте не будут** размещаться тексты книг, только метаданные, аналитика и ссылки на легальное приобретение.

**Уникальное ценностное предложение:** Мы не просто каталогизируем книги, мы создаем карту литературных миров, позволяя читателю проложить маршрут к идеальному произведению.

Создаем сайт-каталог с интеллектуальным анализом книг, включающий:
- Нестандартное описание и тегирование
- Глубокий анализ книг, серий, миров, характеров персонажей
- Выделение главных героев и ключевых сцен
- Генерацию изображений персонажей и батальных сцен
- Создание промо-видео главных сцен
- Удобный поиск по настроению и предпочтениям


## Исходные данные

### Объем библиотеки:
**Русская литература:** ~700 000 книг в формате fb2 (469,20 GB в архивах)
**Иностранная литература:** ~2 млн книг в форматах fb2, epub, mobi
**Средний размер:** 300-400к токенов на книгу, ~8к токенов на главу

### Источники пополнения:
- Ежедневный парсинг новинок с нескольких сайтов
- Ежемесячные дампы коллекций

### Локальная структура источников
3 активных источника (максимум планируется 5):
- /zip_flibusta - ежедневный парсинг + месячные дампы
- /zip_searchfloor - ежедневный парсинг
- /zip_anna - месячные дампы


## Причина появлении идеи проекта:
Современные книжные каталоги используют устаревшие методы классификации, основанные на формальных признаках (автор, жанр, серия), что не позволяет читателям эффективно находить контент, соответствующий их текущим потребностям и настроению. При наличии миллионов книг традиционный подход становится неэффективным.


## Конечная задача:
Сделать сайт с нестандартным описанием, тэгированием, глубоким анализом книг, серий, миров, характеров ГГ и в целом помогающий быстро, удобно и просто выбрать что почитать по настроению и предпочтениям. Планируется выделение ГГ, главных сцен Характером, построение изображений ГГ и батальных сцен, генерация видео роликов  главных сцен и промо книг, для привлечения внимания и фанклуба читателей. На сайте НЕ будет самих книг, только каталог с большим количеством сортировок и выбором. Максимально удовлетворить потребности ищущих что почитать.


## Ключевые функции
- Data Pipeline: Автоматизированный сбор, парсинг, очистка и дедупликация книг из различных источников.
- AI-анализ контента: Извлечение сущностей (персонажи, локации), анализ характеров, определение ключевых сцен, тематик и настроения.
- Семантический поиск (RAG): Создание мощного поискового движка по всему корпусу текстов для ответов на сложные запросы пользователей.
- Генерация контента: Автоматическое создание уникальных описаний, изображений персонажей/сцен и промо-материалов.
- Публичный каталог: Веб-сайт, предоставляющий доступ к результатам анализа, с продвинутыми фильтрами и ссылками на легальные источники для покупки книг. **Тексты самих книг на сайте не размещаются**.

## Техническое решение
### Архитектура:
Локальная обработка: Полный текст книг и векторизация (из-за объемов)
Облачный каталог: Только метаданные и описания

### Технологический стек:
Парсинг: Модульная архитектура с ParserDispatcher для FB2/EPUB/MOBI
Хэширование: MD5 UUID для единого metadata_hash (экономия места в БД)
Чанкинг: LangChain (по параграфам или предложениям)
Векторная БД: Qdrant
Embedding: Локальная модель (из-за объема данных)
Основная БД: PostgreSQL


## **Функциональные требования**

### **1. Подсистема сбора и первичной обработки данных (Ingestion Pipeline)**

**Архитектура:** Producer-Consumer с Redis и 3-очередной системой

#### **1.0. Система восстановления (`run_00_recovery.py`)**
*   **StaleTaskAuditor:** Обнаружение зависших задач в Redis по истекшему таймауту
*   **IncompleteBookAuditor:** Поиск книг со статусом 10 в PostgreSQL без артефактов
*   **Философия:** Безопасный откат вместо доделывания, идемпотентность, --dry-run по умолчанию
*   **Архитектура:** Аудитор целостности состояния между Redis и PostgreSQL

#### **1.1. Сканер-продюсер (`run_10_scan_sources.py`)**
*   **Мониторинг источников:** Автоматическое (cron каждые 1-3 часа) и ручное сканирование 3 источников данных
*   **Извлечение ID:** Регулярным выражением извлекает числовой ID из имени файла (например: `flibusta.826222.zip` → `826222`)
*   **Дедупликация задач:** Проверяет дубликаты в 4 источниках:
    1. Redis очереди: `books:queue:new`, `books:queue:processing`, `books:queue:completed`
    2. Redis кэш: `books:set:processed` (если используется режим --cache)
    3. PostgreSQL: таблица `book_sources` по уникальному индексу `(source_type, source_id)`
*   **Формирование задач:** Создает компактные задачи `{"source_type": 1, "source_id": 826222}` и помещает в `books:queue:new`
*   **Режимы работы:**
    - Обычный: прямые запросы к PostgreSQL (для ежедневных 10-20 файлов)
    - `--cache`: предварительная загрузка в Redis для больших дампов (2000+ файлов)

#### **1.2. Воркер-потребитель (`run_20_process_book_worker.py`)**
*   **Архитектура:** Легковесный воркер с четким разделением ответственностей (SRP)
*   **Ответственности воркера:**
    - Управление задачами в очереди (захват/завершение) 
    - Файловые операции (перемещение между директориями)
    - Обработка ошибок и retry логика
    - Координация между очередью и бизнес-логикой
*   **Бизнес-логика:** Вся обработка книг инкапсулирована в `BookProcessor`:
    - Потоковое чтение архивов через `StorageManager` без распаковки на диск
    - Модульный парсинг через `ParserDispatcher` → `FB2Parser` → `FB2Transformer`
    - Постобработка: генерация ID, очистка данных, сохранение артефактов
    - Хэширование через `HashComputer` (MD5 UUID для экономии места)
    - Дедупликация по единому `metadata_hash` (включает переводчиков)
    - Атомарное сохранение в PostgreSQL
    - Постановка в очередь RAG для дальнейшего анализа
*   **Результат:** Упрощенная архитектура с легким тестированием и поддержкой

### **2. Архитектура данных**

#### **2.1. Redis очереди (3-уровневая система):**

> **📋 Полная документация**: См. [doc/redis_queues.md](redis_queues.md) - единый источник истины по архитектуре очередей Redis и их использованию.


#### **2.2. Файловая структура (новая архитектура):**
```
# Исходные данные (остаются нетронутыми)
/sources/zip_flibusta/
├── flibusta.826222.zip          # Исходные архивы остаются на месте
├── flibusta.826789.zip
└── flibusta.828931.zip

# Результаты обработки (отдельная структура по UUID)
/storage/canonical_json/
├── 01/8f/7b/                    # Многоуровневая структура по UUID
│   └── 018f7b8c-a0f8-7177-8c6a-3a1b5b9d4a1e.json.zst
├── 01/8f/7c/
│   └── 018f7c1d-b2e9-7288-9d7b-4c2c6c0e5b2f.json.zst
└── ...

# Карантин (логический, только PostgreSQL)
# Физических копий файлов больше нет - все записи в таблице quarantined_books
```

#### **2.3. PostgreSQL схема:**
- `books` — основные метаданные книг
- `authors` — справочник авторов с нормализацией
- `book_authors` — связь книг с авторами (многие-ко-многим)
- `book_sources` — источники файлов с уникальным индексом `(source_type, source_id)`

### **2. Подсистема анализа и обогащения контента (RAG Pipeline)**

**Статус:** Планируется к реализации на Этапе 2. Базовая инфраструктура для Ingestion Pipeline уже готова.

#### **Планируемая архитектура:**

*   **2.1. Чанкинг:** Разбиение текста на семантически связанные фрагменты с использованием LangChain
*   **2.2. Векторизация:** Локальная embedding модель для создания векторных представлений
*   **2.3. Векторная БД:** Qdrant для хранения эмбеддингов с метаданными
*   **2.4. LLM анализ:** Извлечение сущностей, анализ персонажей, генерация тегов
*   **2.5. Медиа-контент:** Генерация изображений персонажей и ключевых сцен

#### **Интеграция с текущей системой:**
*   Добавление pipeline-этапа после сохранения в PostgreSQL
*   Использование существующей очередной архитектуры Redis
*   Расширение схемы БД для хранения результатов анализа

### **3. Веб-сайт (Каталог)**

*   **3.1. Поиск и фильтрация:**
    *   Стандартный поиск по автору, названию, серии.
    *   **Семантический поиск:** Поиск по запросу на естественном языке (например: "книга про космического пирата с чувством юмора").
    *   **Многомерная фильтрация:** Система фильтров на основе сгенерированных тегов, характеристик ГГ, атмосферы, темпа и т.д.

*   **3.2. Страницы сущностей:**
    *   **Страница книги:** Полная информация, включая стандартные метаданные, сгенерированную аннотацию, список ГГ, облако тегов, сгенерированные изображения/видео, рейтинг, отзывы, ссылки на покупку.
    *   **Страница автора:** Биография, список всех произведений (включая разные серии и псевдонимы), общая стилистика автора.
    *   **Страница серии/мира:** Описание мира, хронология событий, ключевые персонажи.

## **Нефункциональные требования**

*   **1. Производительность:** Пайплайны обработки должны быть оптимизированы для обработки десятков тысяч книг в сутки. Время отклика сайта не должно превышать 2-3 секунды для сложных запросов.
*   **2. Масштабируемость:** Архитектура должна позволять горизонтальное масштабирование как вычислительных мощностей для обработки, так и баз данных. Система должна быть готова к росту объема данных до десятков миллионов книг.
*   **3. Поддерживаемость:** Код должен быть модульным, документированным. Предусмотреть возможность полного перестроения RAG-базы (векторов и обогащенных данных) раз в 1-2 года при появлении более совершенных embedding-моделей или технологий.




## **Текущий статус реализации (Декабрь 2024)**

### ✅ **Этап 1: MVP Ingestion Pipeline - ЗАВЕРШЕН**

**Реализованные компоненты:**
- **Сканер-продюсер** (`run_10_scan_sources.py`) с режимами --cache, --sync-only, --clear-cache, --quiet
- **Воркер-потребитель** (`run_20_process_book_worker.py`) с модульной архитектурой парсинга:
  - `ParserDispatcher` - диспетчер парсеров по формату файла
  - `FB2Parser` + `FB2Transformer` - полная обработка FB2 в каноническую модель  
  - `HashComputer` - оптимизированное хэширование с MD5 UUID
- **Система восстановления** (`run_00_recovery.py`) - аудитор целостности состояния с модулями StaleTaskAuditor и IncompleteBookAuditor
- **3-очередная архитектура Redis** (new/processing/completed) с постоянным хранением успешных задач
- **Многоуровневое хранение артефактов** по UUID с Zstandard сжатием в `/storage/canonical_json/`
- **Логический карантин** через PostgreSQL без физического копирования файлов
- **Оптимизированная дедупликация** с Redis кэшированием для больших дампов
- **Полная документация** pipeline01.md и pipeline00.md

**Архитектурные особенности:**
- Отказоустойчивость: атомарные операции, восстановление после сбоев
- Масштабируемость: горизонтальное масштабирование воркеров  
- Производительность: Redis кэш (~0.1ms) vs PostgreSQL (~2ms) для дедупликации
- Модульность: парсеры разделены по форматам (FB2/EPUB/MOBI)
- Экономия места: MD5 UUID хэши (36 символов) вместо SHA-256 (64 символа)
- Мониторинг: детальная статистика очередей и файловой системы

### 🚧 **Этап 2: RAG Pipeline - В ПЛАНАХ**
- Чанкинг и векторизация текстов
- Qdrant для векторного поиска  
- LLM анализ содержания и персонажей
- Генерация нестандартных тегов

### 🚧 **Этап 3: Веб-каталог - В ПЛАНАХ**
- Семантический поиск
- Продвинутые фильтры по настроению
- Страницы книг/авторов/серий

---

# Варианты названий для проекта
1. **BookSense** - отражает глубокое понимание и анализ содержания книг
2. **StoryDive** - погружение в истории, глубокий анализ сюжетов и персонажей  
3. **ReadMood** - подбор книг по настроению, эмоциональная составляющая выбора
4. **NovelScope** - широкий обзор и детальный анализ романов
5. **PageMind** - интеллектуальный анализ страниц, AI-подход к каталогизации
6. **PlotSeeker** - поиск по сюжетам, персонажам и ключевым сценам
7. **BookLens** - детальный взгляд на книги через призму анализа
8. **StoryMatch** - подбор историй по предпочтениям и настроению
9. **ChapterAI** - акцент на AI-анализе глав и содержания
10. **TaleForge** - создание нового опыта работы с каталогом книг, "кузница историй"

Каждое название можно дополнить слоганом, например:
- BookSense: *"Feel the story before you read"*
- StoryDive: *"Deep into worlds and characters"*
- ReadMood: *"Find your perfect book match"*