# Система карантина "Аудиторский след" - Операционное руководство

> **Новая архитектура:** Карантин как неизменяемая запись о событии в PostgreSQL. Исходные файлы остаются нетронутыми.

## 📋 Обзор системы

Система карантина "Аудиторский след" обеспечивает надежное отслеживание проблемных книг через PostgreSQL. Карантин - это не физическое место, а **неизменяемая запись о событии** в базе данных.

### Философия новой архитектуры:
- **PostgreSQL - единственный источник правды** о карантинном статусе
- **Никаких файловых операций** - исходные архивы остаются нетронутыми
- **Атомарная запись** в БД через одну транзакцию
- **Аналитическая гибкость** через JSONB детали и SQL запросы
- **Минимальное влияние на производительность** - быстрые индексированные проверки

## 🏗️ Архитектура данных

### Таблица `quarantined_books`

```sql
CREATE TABLE quarantined_books (
    source_type SMALLINT NOT NULL,           -- 1=flibusta, 2=searchfloor, 3=anna
    source_id INT NOT NULL,                  -- ID файла в источнике
    primary_quarantine_type VARCHAR(50),     -- 'trial', 'small_content', 'anthology'
    reason TEXT,                             -- Человекочитаемая причина
    details JSONB,                           -- task_data + detected_anomalies
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (source_type, source_id)    -- Композитный ключ для быстрого поиска
);
```

### Структура JSONB `details`

```json
{
  "archive_path": "/path/to/archive.zip",
  "book_filename": "12345.fb2",
  "archive_mtime": 1640995200.0,
  "detected_anomalies": [
    {
      "type": "trial",
      "reason": "Обнаружен ознакомительный фрагмент"
    },
    {
      "type": "small_content",
      "reason": "Общее количество контента менее 40000 символов"
    }
  ]
}
```

## 📊 Типы карантина

### Приоритет детекторов

Детекторы выстроены в порядке приоритета. Первый сработавший определяет `primary_quarantine_type`:

1. **`trial`** - Ознакомительные фрагменты (высший приоритет)
2. **`small_content`** - Малое количество контента
3. **`anthology`** - Антологии и сборники
4. **`footnotes`** - Проблемы со сносками
5. **`error`** - Системные ошибки обработки

### Описание типов

#### 🚫 `trial` - Ознакомительные фрагменты
- **Причина:** Обнаружены маркеры ознакомительного фрагмента
- **Логика:** Фрагменты нарушают дедупликацию и блокируют загрузку полных версий
- **Действие:** Исходный файл остается на месте, запись в БД

#### 📏 `small_content` - Малое количество контента
- **Причина:** Общее количество контента менее порогового значения
- **Логика:** Книги с недостаточным объемом текста
- **Действие:** Исходный файл остается на месте, запись в БД

#### 📚 `anthology` - Антологии и сборники
- **Причина:** Обнаружена структура антологии/сборника
- **Логика:** Требуют специальной обработки для разделения на отдельные произведения
- **Действие:** Исходный файл остается на месте, запись в БД

#### 📝 `footnotes` - Проблемы со сносками
- **Причина:** Неопределенные ID сносок или структурные аномалии
- **Логика:** Требуют доработки эвристики определения сносок
- **Действие:** Исходный файл остается на месте, запись в БД

#### ❌ `error` - Системные ошибки
- **Причина:** Ошибки парсинга, доступа к файлам, БД и т.д.
- **Логика:** Технические проблемы, требующие анализа
- **Действие:** Исходный файл остается на месте, запись в БД
## 🔧 Техническая реализация

### Поток данных

```mermaid
graph TD
    A[BookProcessor] -->|QuarantineError| B[BookWorker._handle_task_error]
    B --> C[QuarantineProcessor.process]
    C --> D[add_to_quarantine]
    D --> E[PostgreSQL: quarantined_books]

    F[ScannerInventorizer] --> G[_batch_check_quarantine]
    G --> H[get_quarantined_ids]
    H --> E
    H --> I[Фильтрация кандидатов]
```

### Ключевые компоненты

**QuarantineProcessor** - логический процессор карантина
```python
def process(self, task_data: dict, quarantine_type: QuarantineType, reason: str) -> None:
    """Записывает книгу в карантин PostgreSQL."""
    source_type = task_data.get("source_type")
    source_id = task_data.get("source_id")

    details = {
        "archive_path": task_data.get("archive_path"),
        "book_filename": task_data.get("book_filename"),
        "detected_anomalies": [{"type": quarantine_type.value, "reason": reason}]
    }

    add_to_quarantine(source_type, source_id, quarantine_type.value, reason, details)
```

**Функции доступа к данным**
```python
def add_to_quarantine(source_type: int, source_id: int,
                     quarantine_type: str, reason: str, details: dict) -> None:
    """Атомарная запись в карантин с ON CONFLICT DO NOTHING."""

def get_quarantined_ids(candidate_pairs: list[tuple[int, int]]) -> set[tuple[int, int]]:
    """Пакетная проверка карантина для сканера."""
```

### Интеграция в пайплайны

**BookWorker** - обработка QuarantineError
```python
if isinstance(error, QuarantineError):
    self.quarantine_processor.process(task_data, error.quarantine_type, error.message)
```

**ScannerInventorizer** - фильтрация кандидатов
```python
# Шаг 2: Проверка карантина (новый шаг)
candidates_after_quarantine = self._batch_check_quarantine(candidates_after_redis)
```

## 📈 Детекция и критерии

### Детекторы качества

**FragmentDetector** - ознакомительные фрагменты
```python
def is_fragment(self, canonical_book: CanonicalBook) -> bool:
    """Поиск маркеров ознакомительного фрагмента в тексте."""
    # Проверка на ключевые фразы: "ознакомительный фрагмент", "демо-версия" и т.д.
```

**SmallBookDetector** - структурные аномалии
```python
def check_book_structure(self, canonical_book: CanonicalBook) -> QuarantineType | None:
    """Проверка объема контента и структуры книги."""
    total_content = sum(len(ch.content_md) for ch in canonical_book.chapters)
    if total_content < self.min_content_chars:  # по умолчанию 40000
        return QuarantineType.SMALL_CONTENT
```

**AnthologyDetector** - антологии и сборники
```python
def is_anthology(self, canonical_book: CanonicalBook) -> bool:
    """Определение структуры антологии по авторам и главам."""
    # Анализ множественных авторов и структуры глав
```

### Приоритет детекторов в BookProcessor

```python
def _determine_quarantine_type(self, canonical_book: CanonicalBook):
    # Приоритет 1: Ознакомительные фрагменты
    if self.fragment_detector.is_fragment(canonical_book):
        return QuarantineType.TRIAL, reason

    # Приоритет 2: Структурные аномалии
    quarantine_type = self.small_book_detector.check_book_structure(canonical_book)
    if quarantine_type:
        return quarantine_type, reason

    # Приоритет 3: Антологии
    if self.anthology_detector.is_anthology(canonical_book):
        return QuarantineType.ANTHOLOGIES, reason
```

## 🎯 Преимущества новой архитектуры

### Производительность
- **Нет файловых операций** - исходные архивы остаются нетронутыми
- **Быстрые индексированные проверки** в PostgreSQL
- **Пакетные запросы** для оптимизации сканера
- **Минимальное влияние на I/O** системы

### Надежность
- **Атомарные транзакции** - запись в карантин не может быть частичной
- **ON CONFLICT DO NOTHING** - защита от повторных записей
- **Единственный источник правды** - PostgreSQL для всех проверок

### Аналитические возможности
- **JSONB детали** - гибкие запросы по структурированным данным
- **Временные метки** - анализ динамики карантина
- **SQL аналитика** - стандартные инструменты для отчетов

## 🔍 Мониторинг и аналитика

### SQL запросы для анализа

**Общая статистика карантина:**
```sql
SELECT
    primary_quarantine_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM quarantined_books
GROUP BY primary_quarantine_type
ORDER BY count DESC;
```

**Динамика по времени:**
```sql
SELECT
    DATE(updated_at) as date,
    primary_quarantine_type,
    COUNT(*) as daily_count
FROM quarantined_books
WHERE updated_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(updated_at), primary_quarantine_type
ORDER BY date DESC, daily_count DESC;
```

**Анализ деталей (JSONB):**
```sql
-- Топ архивов по количеству проблемных книг
SELECT
    details->>'archive_path' as archive_path,
    COUNT(*) as books_count,
    array_agg(DISTINCT primary_quarantine_type) as quarantine_types
FROM quarantined_books
GROUP BY details->>'archive_path'
HAVING COUNT(*) > 5
ORDER BY books_count DESC;

-- Поиск по аномалиям
SELECT source_type, source_id, reason, details
FROM quarantined_books
WHERE details->'detected_anomalies' @> '[{"type": "trial"}]'
LIMIT 10;
```

### Метрики для мониторинга

- **Коэффициент карантина** - процент книг в карантине от общего числа обработанных
- **Распределение по типам** - баланс между типами проблем
- **Скорость роста карантина** - динамика новых записей
- **Эффективность детекторов** - точность классификации проблем

## 🚨 Операционные процедуры

### Восстановление из карантина

**Удаление из карантина для повторной обработки:**
```sql
-- Удалить конкретную книгу из карантина
DELETE FROM quarantined_books
WHERE source_type = 1 AND source_id = 12345;

-- Удалить все книги определенного типа (например, после исправления детектора)
DELETE FROM quarantined_books
WHERE primary_quarantine_type = 'small_content'
  AND updated_at < '2024-01-01';

-- Массовое удаление по архиву
DELETE FROM quarantined_books
WHERE details->>'archive_path' = 'problematic_archive.zip';
```

**Исходные файлы остаются нетронутыми** - после удаления записи из БД сканер снова найдет файл и поставит в очередь.
### Очистка старых записей

```sql
-- Удаление записей старше 90 дней (для освобождения места в БД)
DELETE FROM quarantined_books
WHERE updated_at < NOW() - INTERVAL '90 days';

-- Архивирование в отдельную таблицу перед удалением
CREATE TABLE quarantined_books_archive AS
SELECT * FROM quarantined_books
WHERE updated_at < NOW() - INTERVAL '90 days';

-- Очистка по типам (например, trial можно удалять чаще)
DELETE FROM quarantined_books
WHERE primary_quarantine_type = 'trial'
  AND updated_at < NOW() - INTERVAL '30 days';
```

### Анализ проблемных файлов

```python
# Использование нового аналитического инструмента
python tools/analyze_quarantine.py --type trial --limit 10
python tools/analyze_quarantine.py --archive-path "problematic.zip"
python tools/analyze_quarantine.py --stats --export-csv
```

## 🔄 Миграция с файлового карантина

При переходе с файлового карантина на PostgreSQL:

```sql
-- Создание записей для существующих файлов карантина
-- (выполняется миграционным скриптом)
INSERT INTO quarantined_books (source_type, source_id, primary_quarantine_type, reason, details)
SELECT
    extract_source_type(file_path),
    extract_source_id(file_path),
    quarantine_folder_name,
    'Migrated from file quarantine',
    jsonb_build_object(
        'archive_path', relative_path,
        'migration_date', NOW(),
        'original_quarantine_path', full_file_path
    )
FROM existing_quarantine_files;
```

---

**📊 Связанная документация:**
- [Архитектура pipeline](../operations/pipeline01.md) - интеграция с дедупликацией
- [Redis очереди](../redis_queues.md) - взаимодействие с системой задач
- [База данных](../database/schema.md) - структура таблиц

**🔧 Инструменты:**
- `tools/analyze_quarantine.py` - анализ карантина PostgreSQL
- `app/processing/quarantine_processor.py` - логический процессор
- `app/database/queries.py` - функции доступа к данным