-- Включаем расширение для полнотекстового поиска
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Таблица произведений (центральный хаб)
CREATE TABLE IF NOT EXISTS books (
    id UUID PRIMARY KEY,
    title TEXT NOT NULL,
    lang VARCHAR(3) NOT NULL,
    metadata_hash UUID  NOT NULL,
    process_status SMALLINT DEFAULT 0 NOT NULL, -- все process_status в doc/redis_queues.md
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
-- Уникальный индекс для дедупликации по единому metadata_hash
CREATE UNIQUE INDEX IF NOT EXISTS uq_books_metadata_hash ON books (metadata_hash);
CREATE INDEX IF NOT EXISTS idx_books_lang ON books (lang);

-- Индекс для быстрого поиска книг в определенном статусе
CREATE INDEX IF NOT EXISTS idx_books_process_status ON books (process_status);

-- Индекс для поиска по названию
CREATE INDEX IF NOT EXISTS idx_books_title_gin ON books USING GIN (title gin_trgm_ops);


-- Авторы
CREATE TABLE IF NOT EXISTS authors (
    id BIGSERIAL PRIMARY KEY,
    first_name VARCHAR(255),
    middle_name VARCHAR(255),
    last_name VARCHAR(255),
    metadata JSONB -- nickname и full_name пишем сюда
);
CREATE UNIQUE INDEX IF NOT EXISTS uq_author_name ON authors (last_name, first_name, middle_name);

-- book_authors - Связь Книги <-> Авторы (многие-ко-многим)
CREATE TABLE IF NOT EXISTS book_authors (
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    author_id INT REFERENCES authors(id) ON DELETE CASCADE,
    
    role_type SMALLINT NOT NULL, -- Роль: 1 = Автор, 2 = Переводчик и т.д.
    PRIMARY KEY (book_id, author_id, role_type)
);


-- Серии/Циклы
CREATE TABLE IF NOT EXISTS series (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type SMALLINT NOT NULL, -- 1=Авторская, 2=Издательская, 3=Межавторская
    metadata JSONB
);
CREATE INDEX IF NOT EXISTS idx_series_name_type ON series (name, type);

CREATE TABLE IF NOT EXISTS series_authors (
    series_id INT NOT NULL REFERENCES series(id) ON DELETE CASCADE,
    author_id INT NOT NULL REFERENCES authors(id) ON DELETE CASCADE,
    PRIMARY KEY (series_id, author_id)
);
CREATE INDEX IF NOT EXISTS idx_series_authors_author_id ON series_authors (author_id);

-- book_series - Связь Книга <-> Серия (многие-ко-многим)
CREATE TABLE IF NOT EXISTS book_series (
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    series_id INT REFERENCES series(id) ON DELETE RESTRICT,
    series_number SMALLINT,
    PRIMARY KEY (book_id, series_id)
);


-- Описания/Аннотации
CREATE TABLE IF NOT EXISTS book_meta (
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    metadata JSONB,
    PRIMARY KEY (book_id)
);


-- Book Sources
CREATE TABLE IF NOT EXISTS book_sources (
    id BIGSERIAL PRIMARY KEY, -- Суррогатный ключ для производительности
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    source_type SMALLINT NOT NULL, -- 1=flibusta, 2=searchfloor, 3=ann, n=other
    source_id INT NOT NULL, -- id в источнике
    metadata JSONB, -- ссылки на рейтинги и магазины
    CONSTRAINT uq_source_identity UNIQUE (source_type, source_id)
);
-- Индекс для быстрого поиска по book_id при JOIN-ах
CREATE INDEX IF NOT EXISTS idx_book_sources_book_id ON book_sources (book_id);

-- Quarantined Books - Аудиторский след карантина
CREATE TABLE IF NOT EXISTS quarantined_books (
    source_type SMALLINT NOT NULL,
    source_id INT NOT NULL,
    primary_quarantine_type VARCHAR(50) NOT NULL, -- 'trial', 'small_content', 'anthology', etc.
    reason TEXT,
    details JSONB, -- Для хранения task_data (archive_path, book_filename) и detected_anomalies
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (source_type, source_id) -- Гарантирует уникальность и быстрый поиск
);
-- Индекс для аналитических запросов по типу карантина
CREATE INDEX IF NOT EXISTS idx_quarantined_books_type ON quarantined_books (primary_quarantine_type);


-- Список категорий. Статическая таблица
CREATE TABLE IF NOT EXISTS genrelist (
  genre_id INT PRIMARY KEY,
  genre_code VARCHAR(45) UNIQUE NOT NULL,
  genre_desc VARCHAR(99) NOT NULL,
  genre_meta VARCHAR(45) NOT NULL
);
-- book_genres - Связь Книга <-> Жанры (многие-ко-многим)
CREATE TABLE IF NOT EXISTS book_genres (
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    genre_id INT REFERENCES genrelist(genre_id) ON DELETE CASCADE,
    PRIMARY KEY (book_id, genre_id)
);


-- Тексты чанков для RAG
CREATE TABLE IF NOT EXISTS book_chunks (
    id bigserial PRIMARY KEY,
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,

    chapter_number smallint  NOT NULL CHECK (chapter_number > 0),
    chunk_number_in_chapter smallint  NOT NULL CHECK (chunk_number_in_chapter > 0),

    -- chunk_text - принято решение хранить чанки с обогащением в json файлах
    ner_metadata jsonb,

    token_count int NOT NULL CHECK (token_count >= 0),  -- или GENERATED

    -- Чётко контролируем уникальность чанка в пределах книги и главы
    CONSTRAINT uq_book_chapter_chunk UNIQUE (book_id, chapter_number, chunk_number_in_chapter)
);

-- Индексы под основные запросы
CREATE INDEX IF NOT EXISTS idx_chunks_book_chap_chunk
    ON book_chunks (book_id, chapter_number, chunk_number_in_chapter);

-- Если бывает «дай все чанки книги» – этот индекс можно оставить,
-- но он становится избыточным, если существует комбинированный.
-- CREATE INDEX idx_chunks_book_only ON book_chunks (book_id);

-- GIN для поиска по NER
CREATE INDEX IF NOT EXISTS idx_chunks_ner_gin
    ON book_chunks
    USING gin (ner_metadata jsonb_path_ops);




-- Book Chapters --
-- Отказались от таблицы book_chapters - Содержимое книг  перенесли в json файлы с главами и метаданными !!!


-- Триггеры
-- Функция, которая будет вызываться триггером
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Применение триггера к таблице `books`.
-- Срабатывает перед каждым UPDATE и обновляет поле `updated_at`.
DROP TRIGGER IF EXISTS set_timestamp ON books;
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON books
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Применение триггера к таблице `quarantined_books`.
-- Срабатывает перед каждым UPDATE и обновляет поле `updated_at`.
DROP TRIGGER IF EXISTS set_timestamp_quarantined ON quarantined_books;
CREATE TRIGGER set_timestamp_quarantined
BEFORE UPDATE ON quarantined_books
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();