# Tools Utils - Общие утилиты для скриптов

## Назначение

Модуль `tools/utils.py` устраняет дублирование логики в скриптах tools/. 

**Проблема:** Ранее каждый скрипт содержал практически идентичный код для:
- Потокового чтения ZIP архивов
- Создания/очистки временных FB2 файлов
- Инициализации проектных компонентов (ParserDispatcher, FragmentDetector)
- Обработки ошибок парсинга

**Решение:** Общие переиспользуемые функции с единой логикой, соответствующей новой архитектуре StorageManager.

## Основные функции

### `process_zip_archive(zip_path, fb2_processor, fb2_filter=None, error_handler=None)`

Универсальная обработка ZIP архивов с потоковым чтением. Заменяет дублированный код в скриптах.
Поддерживает три типа коллбэков для обратной совместимости.

```python
# Новый стиль (рекомендуется) - потоковое чтение
def my_book_analyzer(archive_path: str, fb2_filename: str, fb2_stream: io.BytesIO, mtime: float) -> dict:
    canonical_book = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime)
    return {"title": canonical_book.title, "chapters": len(canonical_book.chapters)}

# Старый стиль - для обратной совместимости
def legacy_analyzer(archive_path: str, fb2_filename: str, temp_fb2_path: Path) -> dict:
    canonical_book = get_canonical_book_from_file(temp_fb2_path)
    return {"title": canonical_book.title, "chapters": len(canonical_book.chapters)}

results = process_zip_archive("/path/to/archive.zip", my_book_analyzer)
```

### `get_canonical_book_from_file(fb2_path, parser_dispatcher=None)`

Получение CanonicalBook из FB2 файла через проектную логику парсинга (legacy).

```python
canonical_book = get_canonical_book_from_file(fb2_path)
# Использует внутренний ParserDispatcher или переданный экземпляр
```

### `get_canonical_book_from_stream(fb2_stream, fb2_filename, file_mtime, parser_dispatcher=None)`

Получение CanonicalBook из потока байтов FB2 (рекомендуется для новой архитектуры).

```python
canonical_book = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime)
# Соответствует архитектуре StorageManager с потоковым чтением
```

### `collect_zip_archives(paths, limit=None)`

Сбор путей к ZIP архивам в указанных директориях.

```python
archives = collect_zip_archives(["/path1", "/path2"], limit=100)
```

### Вспомогательные функции

- `extract_fb2_to_temp(zip_ref, fb2_filename)` - извлечение FB2 во временный файл (legacy)
- `format_file_path(archive_path, fb2_filename)` - стандартное форматирование путей
- `print_processing_status(file_path, status, anomaly_types)` - унифицированный вывод статуса
- `get_components()` - синглтон для переиспользования проектных компонентов

## Рефакторинг существующих скриптов

### До рефакторинга:
```python
# Дублированный код в каждом скрипте:
with zipfile.ZipFile(archive_path, "r") as zip_ref:
    for file_info in zip_ref.infolist():
        if file_info.filename.lower().endswith(".fb2"):
            with tempfile.NamedTemporaryFile(suffix=".fb2", delete=False) as temp_file:
                temp_path = Path(temp_file.name)
            
            with zip_ref.open(file_info.filename) as fb2_file:
                content = fb2_file.read()
            
            with open(temp_path, "wb") as temp_file:
                temp_file.write(content)
            
            parser_dispatcher = ParserDispatcher()
            canonical_book = parser_dispatcher.parse_to_canonical(temp_path)
            
            # ... обработка ...
            
            temp_path.unlink()  # И обработка ошибок
```

### После рефакторинга (новая архитектура):
```python
from tools.utils import process_zip_archive, get_canonical_book_from_stream

def process_book(archive_path: str, fb2_filename: str, fb2_stream: io.BytesIO, mtime: float):
    canonical_book = get_canonical_book_from_stream(fb2_stream, fb2_filename, mtime)
    # ... обработка ...

results = process_zip_archive(archive_path, process_book)
```

**Экономия:** ~50-80 строк дублированного кода на каждый скрипт.

## Принципы

1. **Только проектные компоненты:** Все функции используют существующую логику проекта (ParserDispatcher, CanonicalBook, etc.)
2. **Потоковая архитектура:** Приоритет потокового чтения через io.BytesIO, соответствие StorageManager
3. **Обратная совместимость:** Поддержка legacy коллбэков с временными файлами
4. **Безопасность:** Автоматическая очистка временных файлов (только для legacy режима)
5. **Обработка ошибок:** Стандартизированная обработка QuarantineError и других исключений
6. **Переиспользование:** Синглтон для дорогих компонентов (ParserDispatcher)

## Примеры использования

Смотрите рефакторированный `run_01_analyze_book_structure_single_file.py` как образец применения утилит. 