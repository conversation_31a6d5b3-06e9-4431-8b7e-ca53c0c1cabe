# run_20_process_book_worker.py

import logging
import signal
import sys
import time

import redis
from app import settings

# Удален импорт add_to_processed_cache - теперь кэш обновляется атомарно в queue_manager
from app.processing.book_processor import BookProcessor
from app.processing.error_handler import (
    Error<PERSON><PERSON><PERSON>,
    ProcessingError,
    QuarantineError,
    QuarantineType,
)
from app.processing.quarantine_processor import QuarantineProcessor
from app.processing.queue_manager import TaskQueueManager
from app.storage.local import LocalStorageManager


class BookWorker:
    """Легковесный воркер для обработки книг из очереди Redis.

    Ответственности (Single Responsibility Principle):
    - Управление задачами в очереди (захват/завершение)
    - Логические блокировки через Redis для предотвращения дублирования обработки
    - Обработка ошибок и retry логика
    - Координация между очередью и бизнес-логикой

    Вся бизнес-логика обработки книг инкапсулирована в BookProcessor.
    Исходные файлы остаются на месте, состояние управляется через Redis и PostgreSQL.
    """

    def __init__(self, debug: bool = False):
        """Конструктор.

        Args:
            debug: Включить подробный вывод в stdout. При `False` консоль выводит
                   лишь 1-2 строки на книгу (Ok / Quarantine / Error). Полные
                   логи всегда пишутся в `worker.log`.
        """

        # Настраиваем корневой логгер в зависимости от режима.
        self.setup_logging(debug=debug)

        # Логгер текущего модуля (используется всюду в BookWorker).
        self.logger = logging.getLogger(__name__)

        # Основные компоненты воркера
        self.queue_manager = TaskQueueManager()
        self.storage_manager = LocalStorageManager()
        self.book_processor = BookProcessor(storage_manager=self.storage_manager)
        self.error_handler = ErrorHandler()
        self.quarantine_processor = QuarantineProcessor()

        # Redis соединение для обновления кэша обработанных файлов
        self.redis_client = redis.from_url(settings.REDIS_URL)

        # Флаг для graceful shutdown
        self.should_stop = False
        self.current_task = None

        # Регистрируем обработчики сигналов
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("📚 Воркер с модульной архитектурой готов к работе")
        self.logger.info("🏗️ Бизнес-логика инкапсулирована в BookProcessor")

    def setup_logging(self, debug: bool = False):
        """Гибкая настройка логирования.

        • В *debug*-режиме:
            – Корневой логгер DEBUG → печатает все сообщения в консоль.
            – Формат: с метаданными (timestamp, модуль, уровень).

        • В обычном режиме:
            – Корневой логгер ERROR → подавляет шум от сторонних модулей.
            – В stdout выводятся **только** сообщения самого BookWorker
              уровней INFO / WARNING / ERROR (одна-две строки на книгу).
            – Подробный лог любых уровней всегда идёт в `worker.log`.
        """

        # --------------------------------------------------
        # 1. Общий файл-логгер (всегда полный вывод)
        # --------------------------------------------------
        file_handler = logging.FileHandler("worker.log", encoding="utf-8")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))

        # --------------------------------------------------
        # 2. Настройка root-логгера
        # --------------------------------------------------
        # Всегда храним полный DEBUG-лог в файле. Корневой логгер поэтому
        # остаётся на уровне DEBUG; разницы между debug/production для файла нет.
        logging.basicConfig(level=logging.DEBUG, handlers=[file_handler])

        # --------------------------------------------------
        # 3. Отдельный StreamHandler только для BookWorker
        # --------------------------------------------------
        if not debug:
            # Минимальный формат – лишь текст сообщения.
            stream_handler = logging.StreamHandler(sys.stdout)
            stream_handler.setLevel(logging.INFO)
            stream_handler.setFormatter(logging.Formatter("%(message)s"))

            worker_logger = logging.getLogger(__name__)
            worker_logger.setLevel(logging.INFO)
            worker_logger.addHandler(stream_handler)
            # Чтобы сообщение не доходило до root (иначе оно будет отброшено
            # уровнем ERROR) и не дублировалось в stdout.
            worker_logger.propagate = False
        else:
            # В debug-режиме достаточно root-StreamHandler-а.
            stream_handler = logging.StreamHandler(sys.stdout)
            stream_handler.setLevel(logging.DEBUG)
            stream_handler.setFormatter(logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
            logging.getLogger().addHandler(stream_handler)

    def run(self):
        """Основной цикл воркера"""
        self.logger.info("🚀 Запуск воркера обработки книг")

        processed_count = 0
        start_time = time.time()

        while not self.should_stop:
            try:
                # Захватываем задачу из очереди
                task_data = self.queue_manager.claim_task(timeout=10)

                if task_data is None:
                    # Очередь пуста, короткая пауза
                    time.sleep(1)
                    continue

                self.current_task = task_data

                # Обрабатываем задачу
                success = self._process_task(task_data)

                if success:
                    processed_count += 1
                    self.logger.info(f"📚 Обработано книг: {processed_count}")

                self.current_task = None

                # Выводим статистику каждые 10 книг
                if processed_count > 0 and processed_count % 10 == 0:
                    self._log_statistics(processed_count, start_time)

            except KeyboardInterrupt:
                self.logger.info("⏸️ Получен сигнал остановки")
                break
            except Exception as e:
                self.logger.error(f"❌ Критическая ошибка в основном цикле: {e}", exc_info=True)
                time.sleep(5)  # Пауза перед продолжением

        self.logger.info(f"🏁 Воркер остановлен. Всего обработано: {processed_count} книг")

    def _process_task(self, task_data: dict) -> bool:
        """Обрабатывает одну задачу из очереди через BookProcessor.

        Новая архитектура без файловых операций:
        - Логическая блокировка через Redis для предотвращения дублирования
        - Исходные файлы остаются на месте
        - Передача task_data напрямую в BookProcessor

        Args:
            task_data: Данные задачи с полями:
                - source_type: int
                - source_id: int
                - archive_path: str
                - book_filename: str
                - archive_mtime: float

        Returns:
            True если задача успешно обработана
        """
        source_type = task_data.get("source_type")
        source_id = task_data.get("source_id")
        book_filename = task_data.get("book_filename", "unknown")

        # Формируем ключ блокировки
        lock_key = f"lock:book:{source_type}:{source_id}"
        worker_id = f"worker_{id(self)}"

        try:
            # Логическая блокировка: SET lock_key worker_id NX EX WORKER_TIMEOUT
            lock_acquired = self.redis_client.set(
                lock_key,
                worker_id,
                nx=True,  # Только если ключ не существует
                ex=settings.WORKER_TIMEOUT,  # Автоматическое истечение через WORKER_TIMEOUT секунд
            )

            if not lock_acquired:
                # Книга уже обрабатывается другим воркером
                self.logger.info(f"📚 {book_filename} уже обрабатывается другим воркером, пропускаем")
                return False

            # Основная обработка через BookProcessor
            result = self.book_processor.process(task_data)
            book_title = result.get("title", book_filename)

            # Завершение задачи
            skipped = result.get("status") == "skipped"
            self._complete_task_success(task_data, skipped)

            self.logger.info(f"📚 {book_filename} Ok: {book_title}")
            return True

        except Exception as e:
            # Централизованная обработка ошибок
            self._handle_task_error(e, book_filename, task_data)
            return False

        finally:
            # ОБЯЗАТЕЛЬНО удаляем блокировку в любом случае
            try:
                self.redis_client.delete(lock_key)
            except Exception as cleanup_error:
                self.logger.error(f"Ошибка удаления блокировки {lock_key}: {cleanup_error}")

    def _handle_task_error(self, error: Exception, book_filename: str, task_data: dict) -> bool:
        """Централизованный обработчик ошибок для _process_task.

        В новой архитектуре:
        - Копирует исходный архив в карантин (не перемещает)
        - Читает архив через storage_manager
        - Записывает в соответствующую директорию карантина
        """
        # Получаем решение от ErrorHandler для определения типа карантина (для совместимости)
        # В новой архитектуре используется только для логирования
        self.error_handler.handle_error(error, task_data, "book_processing")

        # Логируем ошибку в зависимости от ее типа
        if isinstance(error, QuarantineError):
            self.logger.warning(f"{book_filename} Quarantine: {error.message}")

            # Новая логика: записываем в PostgreSQL через QuarantineProcessor
            try:
                self.quarantine_processor.process(task_data, error.quarantine_type, error.message)
            except Exception as quarantine_error:
                self.logger.error(f"Ошибка записи в карантин для {book_filename}: {quarantine_error}")

        elif isinstance(error, ProcessingError):
            self.logger.error(f"{book_filename} Error: {error.message}")

            # Системные ошибки тоже отправляем в карантин как ERROR
            try:
                self.quarantine_processor.process(task_data, QuarantineType.ERROR, error.message)
            except Exception as quarantine_error:
                self.logger.error(f"Ошибка записи в карантин для {book_filename}: {quarantine_error}")

        else:
            self.logger.error(f"{book_filename} Error: {error}", exc_info=True)

            # Неизвестные ошибки тоже отправляем в карантин как ERROR
            try:
                self.quarantine_processor.process(task_data, QuarantineType.ERROR, str(error))
            except Exception as quarantine_error:
                self.logger.error(f"Ошибка записи в карантин для {book_filename}: {quarantine_error}")

        # Завершаем задачу в Redis как неуспешную
        self.queue_manager.finalize_task(task_data, reason=str(error))
        return False

    def _complete_task_success(self, task_data: dict, skipped: bool = False):
        """Завершает успешно обработанную задачу.

        В новой архитектуре:
        - Исходные файлы остаются на месте
        - Запись в book_sources — единственный маркер успеха
        - Нет временных файлов для очистки
        """
        reason = "skipped_duplicate" if skipped else "processed"

        # Завершаем задачу в Redis
        self.queue_manager.finalize_task(task_data, reason=reason)

    def _log_statistics(self, processed_count: int, start_time: float):
        """Выводит статистику работы"""
        elapsed = time.time() - start_time
        rate = processed_count / elapsed if elapsed > 0 else 0

        queue_stats = self.queue_manager.get_queue_stats()
        retry_stats = self.error_handler.get_retry_stats()

        self.logger.info(
            f"""
📊 СТАТИСТИКА ВОРКЕРА:
- Обработано книг: {processed_count}
- Скорость: {rate:.2f} книг/сек
- Время работы: {elapsed:.0f} сек
- Очередь новых: {queue_stats["new_tasks"]}
- В обработке: {queue_stats["processing_tasks"]}
- Завершено успешно: {queue_stats["completed_tasks"]}
- Кэш обработанных: {queue_stats["cached_processed"]}
- Файлов с повторами: {retry_stats["files_with_retries"]}
        """
        )

    def _signal_handler(self, signum, frame):
        """Обработчик сигналов для graceful shutdown"""
        self.logger.info(f"📡 Получен сигнал {signum}. Начинаем graceful shutdown...")
        self.should_stop = True

        # Если есть текущая задача, даем ей время завершиться
        if self.current_task:
            self.logger.info("⏳ Ожидаем завершения текущей задачи...")
            # В реальной реализации можно добавить таймаут


def main():
    """Точка входа в приложение"""
    import argparse

    parser = argparse.ArgumentParser(description="Воркер для обработки книг.")
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Включить подробное логирование для отладки.",
    )
    args = parser.parse_args()

    # Создаём воркер с нужным уровнем логирования.
    worker = BookWorker(debug=args.debug)
    worker.run()


if __name__ == "__main__":
    main()
